name: Bug Report
description: "For reporting bugs and other defects"
labels:
  - S-needs-triage
body:
  - type: markdown
    attributes:
      value: >-
        # READ THIS BEFORE YOU POST AN ISSUE!
        
        **This issue tracker is not intended for support requests!** If you need help with crashes or other issues, then
        you should [ask on our Discord server](https://caffeinemc.net/discord) instead. Unless you are certain that you
        have found a defect, and you are able to point to where the problem is, you should not open an issue.
        <br><br>
        Additionally, please make sure you have done the following:
        
        - **Have you ensured that all of your mods (including Sodium) are up-to-date?** The latest version of Sodium
        can always be found [on Modrinth](https://modrinth.com/mod/sodium).
        
        - **Have you read the [list of known driver incompatibilities](https://github.com/CaffeineMC/sodium/wiki/Driver-Compatibility)?** Most problems
        (including "poor performance") are caused by out-of-date or incompatible graphics drivers.
        
        - **Have you used the [search tool](https://github.com/CaffeineMC/sodium/issues) to check whether your issue
        has already been reported?** If it has been, then consider adding more information to the existing issue instead.
        
        - **Have you determined the minimum set of instructions to reproduce the issue?** If your problem only occurs
        with other mods installed, then you should narrow down exactly which mods are causing the issue. Please do not
        provide your entire list of mods to us and expect that we will be able to figure out the problem.
  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: >-
        Use this section to describe the issue you are experiencing in as much depth as possible. The description should
        explain what behavior you were expecting, and why you believe the issue to be a bug. If the issue you are reporting
        only occurs with specific mods installed, then provide the name and version of each mod.

        **Hint:** If you have any screenshots, videos, or other information that you feel is necessary to
        explain the issue, you can attach them here.
  - type: textarea
    id: description-reproduction-steps
    attributes:
      label: Reproduction Steps
      description: >-
        Provide as much information as possible on how to reproduce this bug. Make sure your instructions are as clear and
        concise as possible, because other people will need to be able to follow your guide in order to re-create the issue.
        
        **Hint:**  A common way to fill this section out is to write a step-by-step guide.
    validations:
      required: true
  - type: textarea
    id: log-file
    attributes:
      label: Log File
      description: >-
        **Hint:** You can usually find the log files within the folder `.minecraft/logs`. Most often, you will want the `latest.log`
        file, since that file belongs to the last played session of the game.
      placeholder: >-
        Drag-and-drop the log file here.
    validations:
      required: true
  - type: textarea
    id: crash-report-file
    attributes:
      label: Crash Report
      description: >-
        **Important:** Even if your game is _not crashing_ as a result of your problem, we still need a crash report. This
        file contains important information about your hardware and software configuration. You can generate a crash report
        manually by loading the game to the main menu, and holding the F3+C keys together for approximately 10 seconds.
        
        **Hint:** You can usually find crash reports (with their date and time) in the folder `.minecraft/crash-reports`.
      placeholder: >-
        Drag-and-drop the crash report file here.
    validations:
      required: true
