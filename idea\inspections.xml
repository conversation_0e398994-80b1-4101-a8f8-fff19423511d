<component name="InspectionProjectProfileManager">
    <profile version="1.0">
        <option name="myName" value="CaffeineMC/Sodium"/>
        <inspection_tool class="CStyleArrayDeclaration" enabled="true" level="ERROR" enabled_by_default="true"/>
        <inspection_tool class="EqualsCalledOnEnumConstant" enabled="true" level="WARNING" enabled_by_default="true"/>
        <inspection_tool class="FallthruInSwitchStatement" enabled="true" level="WARNING" enabled_by_default="true"/>
        <inspection_tool class="MissingOverrideAnnotation" enabled="true" level="ERROR" enabled_by_default="true">
            <option name="ignoreObjectMethods" value="false"/>
            <option name="ignoreAnonymousClassMethods" value="false"/>
        </inspection_tool>
        <inspection_tool class="SizeReplaceableByIsEmpty" enabled="true" level="WARNING" enabled_by_default="true"/>
        <inspection_tool class="UnnecessarySemicolon" enabled="true" level="ERROR" enabled_by_default="true"/>
        <inspection_tool class="UnqualifiedFieldAccess" enabled="true" level="WARNING" enabled_by_default="true"/>
        <inspection_tool class="UnqualifiedMethodAccess" enabled="true" level="WARNING" enabled_by_default="true"/>
    </profile>
</component>
