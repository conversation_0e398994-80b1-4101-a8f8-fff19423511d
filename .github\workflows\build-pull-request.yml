# Used when building external pull requests
# We don't want to publish build artifacts or expose our other caches to possibly untrusted code
name: build-pull-request

on: [ pull_request ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: gradle/actions/wrapper-validation@v3
      - uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 21

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-read-only: true

      - name: Execute Gradle build
        run: ./gradlew build

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: sodium-artifacts-${{ steps.ref.outputs.branch }}
          path: build/mods/*.jar