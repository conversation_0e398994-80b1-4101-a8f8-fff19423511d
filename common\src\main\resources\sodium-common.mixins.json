{"package": "net.caffeinemc.mods.sodium.mixin", "required": true, "compatibilityLevel": "JAVA_17", "plugin": "net.caffeinemc.mods.sodium.mixin.SodiumMixinPlugin", "injectors": {"defaultRequire": 1}, "overwrites": {"conformVisibility": true}, "client": ["core.GlCommandEncoderAccessor", "core.MinecraftMixin", "core.WindowMixin", "core.gui.LevelLoadStatusManagerMixin", "core.model.colors.BlockColorsMixin", "core.model.TextureAtlasSpriteMixin", "core.render.BlockEntityTypeMixin", "core.render.TextureAtlasMixin", "core.render.VertexFormatMixin", "core.render.frustum.FrustumMixin", "core.render.immediate.consumer.BufferBuilderMixin", "core.render.immediate.consumer.EntityOutlineGeneratorMixin", "core.render.immediate.consumer.SheetedDecalTextureGeneratorMixin", "core.render.immediate.consumer.SpriteCoordinateExpanderMixin", "core.render.immediate.consumer.VertexMultiConsumerMixin$DoubleMixin", "core.render.immediate.consumer.VertexMultiConsumerMixin$MultipleMixin", "core.render.texture.TextureAtlasAccessor", "core.render.world.ChunkSectionsToRenderMixin", "core.render.world.EntityRendererAccessor", "core.render.world.FogRendererMixin", "core.render.world.GameRendererMixin", "core.render.world.RenderBuffersMixin", "core.render.world.LevelRendererMixin", "core.world.biome.ClientLevelMixin", "core.world.chunk.ZeroBitStorageMixin", "core.world.chunk.SimpleBitStorageMixin", "core.world.chunk.PalettedContainerMixin", "core.world.map.ClientChunkCacheMixin", "core.world.map.ClientPacketListenerMixin", "core.world.map.ClientLevelMixin", "features.gui.hooks.console.GameRendererMixin", "features.gui.hooks.debug.DebugScreenOverlayMixin", "features.gui.hooks.settings.OptionsScreenMixin", "features.gui.screen.LevelLoadingScreenMixin", "features.options.overlays.GuiMixin", "features.options.render_layers.LeavesBlockMixin", "features.options.render_layers.ItemBlockRenderTypesMixin", "features.options.weather.LevelRendererMixin", "features.render.entity.CubeMixin", "features.render.entity.ModelPartMixin", "features.render.entity.cull.EntityRendererMixin", "features.render.entity.shadows.EntityRenderDispatcherMixin", "features.render.frapi.BakedModelMixin", "features.render.frapi.BlockModelPartMixin", "features.render.frapi.BlockRenderDispatcherAccessor", "features.render.frapi.BlockRenderDispatcherMixin", "features.render.frapi.ModelBlockRendererMixin", "features.render.frapi.ModelBlockRendererAccessor", "features.render.frapi.ItemRenderStateMixin", "features.render.frapi.ItemLayerRenderStateMixin", "features.render.frapi.ItemRendererAccessor", "features.render.gui.font.BakedGlyphMixin", "features.render.gui.outlines.LevelRendererMixin", "features.render.immediate.DirectionMixin", "features.render.immediate.buffer_builder.intrinsics.BufferBuilderMixin", "features.render.immediate.buffer_builder.sorting.MeshDataAccessor", "features.render.immediate.buffer_builder.sorting.MultiBufferSourceMixin", "features.render.immediate.buffer_builder.sorting.VertexSortingMixin", "features.render.immediate.matrix_stack.VertexConsumerMixin", "features.render.model.ItemBlockRenderTypesMixin", "features.render.model.item.ItemRendererMixin", "features.render.particle.SingleQuadParticleMixin", "features.render.world.clouds.LevelRendererMixin", "features.render.world.sky.FogRendererMixin", "features.render.world.sky.ClientLevelMixin", "features.render.world.sky.LevelRendererMixin", "features.textures.NativeImageAccessor", "features.textures.SpriteContentsInvoker", "features.textures.animations.tracking.ModelBlockRendererMixin", "features.textures.animations.tracking.GuiGraphicsMixin", "features.textures.animations.tracking.TextureAtlasMixin", "features.textures.animations.tracking.TextureSheetParticleMixin", "features.textures.animations.tracking.AnimatedTextureAccessor", "features.textures.animations.tracking.SpriteContentsFrameInfoAccessor", "features.textures.animations.tracking.SpriteContentsTickerMixin", "features.textures.animations.tracking.SpriteContentsMixin", "features.textures.animations.upload.SpriteContentsAccessor", "features.textures.animations.upload.SpriteContentsAnimatedTextureAccessor", "features.textures.animations.upload.SpriteContentsFrameInfoAccessor", "features.textures.animations.upload.SpriteContentsTickerAccessor", "features.textures.animations.upload.SpriteContentsInterpolationMixin", "features.textures.mipmaps.MipmapGeneratorMixin", "features.textures.mipmaps.SpriteContentsMixin", "features.textures.scan.SpriteContentsMixin", "features.textures.scan.TextureAtlasSpriteMixin", "workarounds.context_creation.WindowMixin", "workarounds.context_creation.RenderSystemMixin", "workarounds.event_loop.RenderSystemMixin"]}