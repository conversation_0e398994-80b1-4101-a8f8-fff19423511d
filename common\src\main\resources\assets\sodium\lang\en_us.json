{"sodium.option_impact.low": "Low", "sodium.option_impact.medium": "Medium", "sodium.option_impact.high": "High", "sodium.option_impact.extreme": "Extreme", "sodium.option_impact.varies": "Varies", "sodium.options.pages.quality": "Quality", "sodium.options.pages.performance": "Performance", "sodium.options.pages.advanced": "Advanced", "sodium.options.view_distance.tooltip": "The render distance controls how far away terrain will be rendered. Shorter distances mean that less terrain will be rendered, improving frame rates.", "sodium.options.simulation_distance.tooltip": "The simulation distance controls how far away terrain and entities will be loaded and ticked. Shorter distances can reduce the internal server's load and may improve frame rates.", "sodium.options.brightness.tooltip": "Controls the minimum brightness in the world. When increased, darker areas of the world will appear brighter. This does not affect the brightness of already well-lit areas.", "sodium.options.gui_scale.tooltip": "Sets the maximum scale factor to be used for the user interface. If \"auto\" is used, then the largest scale factor will always be used.", "sodium.options.fullscreen.tooltip": "If enabled, the game will display in full-screen (if supported).", "sodium.options.fullscreen_resolution.tooltip": "The monitor resolution and refresh rate to be used when in fullscreen mode. Changing this option may interfere with other applications and cause a delay when switching between applications.\n\nThis is only supported on the Windows operating system.", "sodium.options.v_sync.tooltip": "If enabled, the game's frame rate will be synchronized to the monitor's refresh rate, making for a generally smoother experience at the expense of overall input latency. This setting might reduce performance if your system is too slow.", "sodium.options.fps_limit.tooltip": "Limits the maximum number of frames per second. This can help reduce battery usage and system load when multi-tasking. If VSync is enabled, this option will be ignored unless it is lower than your display's refresh rate.", "sodium.options.view_bobbing.tooltip": "If enabled, the player's view will sway and bob when moving around. Players who experience motion sickness while playing may benefit from disabling this.", "sodium.options.attack_indicator.tooltip": "Controls where the Attack Indicator is displayed on screen.", "sodium.options.autosave_indicator.tooltip": "If enabled, an indicator will be shown when the game is saving the world to disk.", "sodium.options.graphics_quality.tooltip": "The default graphics quality controls some legacy options and is necessary for mod compatibility. If the options below are left to \"Default\", they will use this setting.", "sodium.options.clouds_quality.tooltip": "The quality level used for rendering clouds in the sky. Even though the options are labeled \"Fast\" and \"Fancy\", the effect on performance is negligible.", "sodium.options.clouds_distance.tooltip": "The distance clouds will render to. This has minimal impact on performance, and is meant for visual purposes.", "sodium.options.weather_quality.tooltip": "Controls the distance that weather effects, such as rain and snow, will be rendered.", "sodium.options.leaves_quality.name": "Leaves", "sodium.options.leaves_quality.tooltip": "Controls whether leaves will be rendered as transparent (fancy) or opaque (fast).", "sodium.options.particle_quality.tooltip": "Controls the maximum number of particles which can be present on screen at any one time.", "sodium.options.smooth_lighting.tooltip": "Enables the smooth lighting and shading of blocks in the world. This can very slightly increase the amount of time it takes to load or update a chunk, but it doesn't affect frame rates.", "sodium.options.biome_blend.value": "%s block(s)", "sodium.options.biome_blend.tooltip": "The distance (in blocks) which biome colors are smoothly blended across. Using higher values will greatly increase the amount of time it takes to load or update chunks, for diminishing improvements in quality.", "sodium.options.entity_distance.tooltip": "The render distance multiplier used by entity rendering. Smaller values decrease, and larger values increase, the maximum distance at which entities will be rendered.", "sodium.options.entity_shadows.tooltip": "If enabled, basic shadows will be rendered beneath mobs and other entities.", "sodium.options.vignette.name": "Vignette", "sodium.options.vignette.tooltip": "If enabled, a vignette effect will be applied when the player is in darker areas, which makes the overall image darker and more dramatic.", "sodium.options.mipmap_levels.tooltip": "Controls the number of mipmaps which will be used for block model textures. Higher values provide better rendering of blocks in the distance, but could adversely affect performance with resource packs that use many animated textures.", "sodium.options.use_block_face_culling.name": "Use Block Face Culling", "sodium.options.use_block_face_culling.tooltip": "If enabled, only the faces of blocks which are facing the camera will be submitted for rendering. This can eliminate a large number of block faces very early in the rendering process, which greatly improves rendering performance. Some resource packs may have issues with this option, so try disabling it if you're seeing holes in blocks.", "sodium.options.use_fog_occlusion.name": "Use Fog Occlusion", "sodium.options.use_fog_occlusion.tooltip": "If enabled, chunks which are determined to be fully hidden by fog effects will not be rendered, helping to improve performance. The improvement can be more dramatic when fog effects are heavier (such as while underwater), but it may cause undesirable visual artifacts between the sky and fog in some scenarios.", "sodium.options.use_entity_culling.name": "Use Entity Culling", "sodium.options.use_entity_culling.tooltip": "If enabled, entities which are within the camera viewport, but not inside of a visible chunk, will be skipped during rendering. This optimization uses the visibility data which already exists for chunk rendering and does not add overhead.", "sodium.options.animate_only_visible_textures.name": "Animate Only Visible Textures", "sodium.options.animate_only_visible_textures.tooltip": "If enabled, only the animated textures which are determined to be visible in the current image will be updated. This can provide a significant performance improvement on some hardware, especially with heavier resource packs. If you experience issues with some textures not being animated, try disabling this option.", "sodium.options.cpu_render_ahead_limit.name": "CPU Render-Ahead Limit", "sodium.options.cpu_render_ahead_limit.tooltip": "For debugging only. Specifies the maximum number of frames which can be in-flight to the GPU. Changing this value is not recommended, as very low or high values may create frame rate instability.", "sodium.options.cpu_render_ahead_limit.value": "%s frame(s)", "sodium.options.performance_impact_string": "Performance Impact: %s", "sodium.options.use_persistent_mapping.name": "Use Persistent Mapping", "sodium.options.use_persistent_mapping.tooltip": "For debugging only. If enabled, persistent memory mappings will be used for the staging buffer so that unnecessary memory copies can be avoided. Disabling this can be useful for narrowing down the cause of graphical corruption.\n\nRequires OpenGL 4.4 or ARB_buffer_storage.", "sodium.options.chunk_update_threads.name": "Chunk Update Threads", "sodium.options.chunk_update_threads.tooltip": "Specifies the number of threads to use for chunk building and sorting. Using more threads can speed up chunk loading and update speed, but may negatively impact frame times. The default value is usually good enough for all situations.", "sodium.options.always_defer_chunk_updates.name": "Always Defer Chunk Updates", "sodium.options.always_defer_chunk_updates.tooltip": "If enabled, rendering will never wait for chunk updates to finish, even if they are important. This can greatly improve frame rates in some scenarios, but it may create significant visual lag where blocks take a while to appear or disappear.", "sodium.options.use_no_error_context.name": "Use No Error Context", "sodium.options.use_no_error_context.tooltip": "When enabled, the OpenGL context will be created with error checking disabled. This slightly improves rendering performance, but it can make debugging sudden unexplained crashes much harder.", "sodium.options.buttons.undo": "Undo", "sodium.options.buttons.apply": "Apply", "sodium.options.buttons.donate": "Buy us a coffee!", "sodium.console.game_restart": "The game must be restarted to apply one or more video settings!", "sodium.console.broken_nvidia_driver": "Your NVIDIA graphics drivers are out of date!\n  * This will cause severe performance issues and crashes when Sodium is installed.\n  * Please update your graphics drivers to the latest version (version 536.23 or newer.)", "sodium.console.core_shaders_error": "The following resource packs are incompatible with Sodium:", "sodium.console.core_shaders_warn": "The following resource packs may be incompatible with Sodium:", "sodium.console.core_shaders_info": "Check the game log for detailed information.", "sodium.console.config_not_loaded": "The configuration file for Sodium has been corrupted, or is currently unreadable. Some options have been temporarily reset to their defaults. Please open the Video Settings screen to resolve this problem.", "sodium.console.config_file_was_reset": "The config file has been reset to known-good defaults."}