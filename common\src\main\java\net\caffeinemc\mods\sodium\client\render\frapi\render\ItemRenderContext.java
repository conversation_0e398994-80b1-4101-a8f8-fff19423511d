/*
 * Copyright (c) 2016, 2017, 2018, 2019 FabricMC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package net.caffeinemc.mods.sodium.client.render.frapi.render;

import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.MatrixUtil;
import net.caffeinemc.mods.sodium.api.texture.SpriteUtil;
import net.caffeinemc.mods.sodium.api.util.ColorMixer;
import net.caffeinemc.mods.sodium.client.render.frapi.SodiumRenderer;
import net.caffeinemc.mods.sodium.client.render.frapi.helper.ColorHelper;
import net.caffeinemc.mods.sodium.client.render.frapi.mesh.EncodingFormat;
import net.caffeinemc.mods.sodium.client.render.frapi.mesh.MeshViewImpl;
import net.caffeinemc.mods.sodium.client.render.frapi.mesh.MutableQuadViewImpl;
import net.caffeinemc.mods.sodium.client.render.texture.SpriteFinderCache;
import net.caffeinemc.mods.sodium.mixin.features.render.frapi.ItemRendererAccessor;
import net.fabricmc.fabric.api.renderer.v1.mesh.QuadEmitter;
import net.fabricmc.fabric.api.renderer.v1.render.RenderLayerHelper;
import net.minecraft.client.renderer.LightTexture;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.Sheets;
import net.minecraft.client.renderer.block.model.BakedQuad;
import net.minecraft.client.renderer.block.model.BlockStateModel;
import net.minecraft.client.renderer.chunk.ChunkSectionLayer;
import net.minecraft.client.renderer.entity.ItemRenderer;
import net.minecraft.client.renderer.item.ItemStackRenderState;
import net.minecraft.util.RandomSource;
import net.minecraft.world.item.ItemDisplayContext;
import net.minecraft.world.level.levelgen.SingleThreadedRandomSource;
import org.jetbrains.annotations.Nullable;
import org.joml.Matrix3f;
import org.joml.Matrix4f;

import java.util.Arrays;
import java.util.List;
import java.util.function.Supplier;

/**
 * The render context used for item rendering.
 */
public class ItemRenderContext extends AbstractRenderContext {
    public static final ThreadLocal<ItemRenderContext> POOL = ThreadLocal.withInitial(ItemRenderContext::new);
    /** Value vanilla uses for item rendering.  The only sensible choice, of course.  */
    private static final long ITEM_RANDOM_SEED = 42L;
    private static final int GLINT_COUNT = ItemStackRenderState.FoilType.values().length;

    public class ItemEmitter extends MutableQuadViewImpl {
        {
            data = new int[EncodingFormat.TOTAL_STRIDE];
            clear();
        }


        @Override
        public void emitDirectly() {
            renderQuad(this);
        }

        public boolean hasTransforms() {
            return activeTransform != NO_TRANSFORM;
        }
    }

    private final MutableQuadViewImpl editorQuad = new ItemEmitter();

    public ItemRenderContext() {}

    private final RandomSource random = new SingleThreadedRandomSource(ITEM_RANDOM_SEED);
    private final Supplier<RandomSource> randomSupplier = () -> {
        random.setSeed(ITEM_RANDOM_SEED);
        return random;
    };

    private ItemDisplayContext transformMode;
    private PoseStack poseStack;
    private Matrix4f matPosition;
    private boolean trustedNormals;
    private Matrix3f matNormal;
    private MultiBufferSource bufferSource;
    private int lightmap;
    private int overlay;
    private int[] colors;

    private RenderType defaultLayer;
    private ItemStackRenderState.FoilType defaultGlint;

    private PoseStack.Pose specialGlintEntry;
    private final VertexConsumer[] vertexConsumerCache = new VertexConsumer[3 * GLINT_COUNT];

    @Override
    public QuadEmitter getEmitter() {
        editorQuad.clear();
        return editorQuad;
    }

    public void renderItem(ItemDisplayContext displayContext, PoseStack poseStack, MultiBufferSource bufferSource, int lightmap, int overlay, int[] colors, List<BakedQuad> vanillaQuads, MeshViewImpl mesh, RenderType layer, ItemStackRenderState.FoilType glint) {
        this.transformMode = displayContext;
        matPosition = poseStack.last().pose();
        this.poseStack = poseStack;

        trustedNormals = this.poseStack.last().trustedNormals;
        matNormal = this.poseStack.last().normal();
        this.bufferSource = bufferSource;
        this.lightmap = lightmap;
        this.overlay = overlay;
        this.colors = colors;

        defaultLayer = layer;
        defaultGlint = glint;

        bufferQuads(vanillaQuads, mesh);

        this.poseStack = null;
        this.bufferSource = null;
        this.colors = null;

        this.specialGlintEntry = null;
        Arrays.fill(vertexConsumerCache, null);
    }


    private void bufferQuads(List<BakedQuad> vanillaQuads, MeshViewImpl mesh) {
        QuadEmitter emitter = getEmitter();

        final int vanillaQuadCount = vanillaQuads.size();

        for (int j = 0; j < vanillaQuadCount; j++) {
            final BakedQuad q = vanillaQuads.get(j);
            emitter.fromBakedQuad(q);
            emitter.emit();
        }

        mesh.outputTo(emitter);
    }

    private void renderQuad(MutableQuadViewImpl quad) {
        final boolean emissive = quad.emissive();
        final VertexConsumer vertexConsumer = getVertexConsumer(quad.renderLayer(), quad.glint());

        tintQuad(quad);
        shadeQuad(quad, emissive);
        bufferQuad(quad, vertexConsumer);
    }

    private void tintQuad(MutableQuadViewImpl quad) {
        final int tintIndex = quad.tintIndex();

        if (tintIndex != -1 && tintIndex < colors.length) {
            final int color = colors[tintIndex];

            for (int i = 0; i < 4; i++) {
                quad.color(i, ColorMixer.mulComponentWise(color, quad.color(i)));
            }
        }
    }

    private void shadeQuad(MutableQuadViewImpl quad, boolean emissive) {
        if (emissive) {
            for (int i = 0; i < 4; i++) {
                quad.lightmap(i, LightTexture.FULL_BRIGHT);
            }
        } else {
            final int lightmap = this.lightmap;

            for (int i = 0; i < 4; i++) {
                quad.lightmap(i, ColorHelper.maxBrightness(quad.lightmap(i), lightmap));
            }
        }
    }

    private void bufferQuad(MutableQuadViewImpl quad, VertexConsumer vertexConsumer) {
        QuadEncoder.writeQuadVertices(quad, vertexConsumer, overlay, matPosition, trustedNormals, matNormal);
        var sprite = quad.sprite(SpriteFinderCache.forBlockAtlas());
        if (sprite != null) {
            SpriteUtil.INSTANCE.markSpriteActive(sprite);
        }
    }

    /**
     * Caches custom blend mode / vertex consumers and mimics the logic
     * in {@code RenderLayers.getEntityBlockLayer}. Layers other than
     * translucent are mapped to cutout.
     */
    private VertexConsumer getVertexConsumer(@Nullable ChunkSectionLayer blendMode, @Nullable ItemStackRenderState.FoilType glintMode) {
        RenderType type;
        ItemStackRenderState.FoilType glint;

        if (blendMode == null) {
            type = defaultLayer;
        } else {
            type = RenderLayerHelper.getEntityBlockLayer(blendMode);
        }

        if (glintMode == null) {
            glint = defaultGlint;
        } else {
            glint = glintMode;
        }

        int cacheIndex;

        if (type == Sheets.translucentItemSheet()) {
            cacheIndex = 0;
        } else if (type == Sheets.cutoutBlockSheet()) {
            cacheIndex = GLINT_COUNT;
        } else {
            cacheIndex = 2 * GLINT_COUNT;
        }

        cacheIndex += glint.ordinal();
        VertexConsumer vertexConsumer = vertexConsumerCache[cacheIndex];

        if (vertexConsumer == null) {
            vertexConsumer = createVertexConsumer(type, glint);
            vertexConsumerCache[cacheIndex] = vertexConsumer;
        }

        return vertexConsumer;
    }

    private VertexConsumer createVertexConsumer(RenderType type, ItemStackRenderState.FoilType glint) {
        if (glint == ItemStackRenderState.FoilType.SPECIAL) {
            if (specialGlintEntry == null) {
                specialGlintEntry = poseStack.last().copy();

                if (transformMode == ItemDisplayContext.GUI) {
                    MatrixUtil.mulComponentWise(specialGlintEntry.pose(), 0.5F);
                } else if (transformMode.firstPerson()) {
                    MatrixUtil.mulComponentWise(specialGlintEntry.pose(), 0.75F);
                }
            }

            return ItemRendererAccessor.sodium$getSpecialFoilBuffer(bufferSource, type, specialGlintEntry);
        }

        return ItemRenderer.getFoilBuffer(bufferSource, type, true, glint != ItemStackRenderState.FoilType.NONE);
    }

    /** used to accept a method reference from the ItemRenderer. */
    @FunctionalInterface
    public interface VanillaModelBufferer {
        void accept(BlockStateModel model, int[] colirs, int color, int overlay, PoseStack matrixStack, VertexConsumer buffer);
    }
}
