{"ambientocclusion": false, "textures": {"top": "block/hopper_top", "particle": "block/hopper_outside", "side": "block/hopper_outside", "inside": "block/hopper_inside"}, "elements": [{"from": [6, 0, 6], "to": [10, 4, 10], "faces": {"north": {"uv": [6, 12, 10, 16], "texture": "#side"}, "east": {"uv": [6, 12, 10, 16], "texture": "#side"}, "south": {"uv": [6, 12, 10, 16], "texture": "#side"}, "west": {"uv": [6, 12, 10, 16], "texture": "#side"}, "down": {"uv": [6, 6, 10, 10], "texture": "#inside", "cullface": "down"}}}, {"from": [4, 4, 4], "to": [12, 10, 12], "faces": {"north": {"uv": [4, 6, 12, 12], "texture": "#side"}, "east": {"uv": [4, 6, 12, 12], "texture": "#side"}, "south": {"uv": [4, 6, 12, 12], "texture": "#side"}, "west": {"uv": [4, 6, 12, 12], "texture": "#side"}, "down": {"uv": [4, 4, 12, 12], "texture": "#inside"}}}, {"from": [0, 10, 0], "to": [16, 16, 16], "faces": {"north": {"uv": [0, 0, 16, 6], "texture": "#side", "cullface": "north"}, "east": {"uv": [0, 0, 16, 6], "texture": "#side", "cullface": "east"}, "south": {"uv": [0, 0, 16, 6], "texture": "#side", "cullface": "south"}, "west": {"uv": [0, 0, 16, 6], "texture": "#side", "cullface": "west"}, "down": {"uv": [0, 0, 16, 16], "texture": "#inside"}}}, {"from": [0, 16, 0], "to": [14, 16, 2], "faces": {"up": {"uv": [0, 0, 14, 2], "texture": "#top", "cullface": "up"}}}, {"from": [14, 16, 0], "to": [16, 16, 14], "faces": {"up": {"uv": [14, 0, 16, 14], "texture": "#top", "cullface": "up"}}}, {"from": [2, 16, 14], "to": [16, 16, 16], "faces": {"up": {"uv": [2, 14, 16, 16], "texture": "#top", "cullface": "up"}}}, {"from": [0, 16, 2], "to": [2, 16, 16], "faces": {"up": {"uv": [0, 2, 2, 16], "texture": "#top", "cullface": "up"}}}, {"from": [2, 11, 2], "to": [14, 16, 2], "faces": {"south": {"uv": [2, 0, 14, 5], "texture": "#side", "cullface": "up"}}}, {"from": [14, 11, 2], "to": [14, 16, 14], "faces": {"west": {"uv": [2, 0, 14, 5], "texture": "#side", "cullface": "up"}}}, {"from": [2, 11, 14], "to": [14, 16, 14], "faces": {"north": {"uv": [2, 0, 14, 5], "texture": "#side", "cullface": "up"}}}, {"from": [2, 11, 2], "to": [2, 16, 14], "faces": {"east": {"uv": [2, 0, 14, 5], "texture": "#side", "cullface": "up"}}}, {"from": [2, 11, 2], "to": [14, 11, 14], "faces": {"up": {"uv": [2, 2, 14, 14], "texture": "#inside", "cullface": "up"}}}]}