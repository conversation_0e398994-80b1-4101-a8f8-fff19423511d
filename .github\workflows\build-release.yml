# Used when a release is pushed to GitHub
# This does not make use of any caching as to ensure a clean build
name: build-release

on:
  release:
     types:
       - published

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 21

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-read-only: true

      - name: Execute Gradle build
        run: ./gradlew build -Pbuild.release=true

      - name: Upload assets to GitHub
        uses: AButler/upload-release-assets@v2.0
        with:
          files: 'build/mods/*.jar'
          repo-token: ${{ secrets.GITHUB_TOKEN }}