modLoader = "javafml"
loaderVersion = "[4,)"
license = "Polyform-Shield-1.0.0"

[[mods]]
modId = "sodium"

version = "${version}"
displayName = "Sodium"

logoFile = "sodium-icon.png" #optional

authors = "Jelly<PERSON><PERSON> (jellysquid3), IMS212"

credits = "by<PERSON><PERSON>, PepperCode1, <PERSON>y<PERSON>eese, <PERSON><PERSON>si, Grayray75, Madis<PERSON>, Johni0702, comp500, coderbot16, Moulberry, MCRcortex, Altirix, embeddedt, pajicadvance, Kroppeb, douira, burgerindividual, TwistedZero, Leo40Git, haykam821, muzikbike"

description = '''
Sodium is a powerful rendering engine for Minecraft which improves frame rates and reduces lag spikes.
'''

provides = ["indium"]

[modproperties.sodium]
"fabric-renderer-api-v1:contains_renderer" = true

[[dependencies.sodium]]
modId = "minecraft"
type = "required"
versionRange = "1.21.4"
ordering = "NONE"
side = "CLIENT"

[[dependencies.sodium]]
modId = "neoforge"
type = "required"
versionRange = "[21.4.3-beta,)"
ordering = "NONE"
side = "CLIENT"

[[dependencies.sodium]]
modId = "embeddium"
type = "incompatible"
reason = "Sodium and Embeddium cannot be used together. Please remove Embeddium."
versionRange = "[0.0.1,)"
ordering = "NONE"
side = "CLIENT"

[[mixins]]
config = "sodium-common.mixins.json"

[[mixins]]
config = "sodium-neoforge.mixins.json"