# Used when a commit is pushed to the repository
# This makes use of caching for faster builds and uploads the resulting artifacts
name: build-commit

on: [ push ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Extract current branch name
        shell: bash
        # bash pattern expansion to grab branch name without slashes
        run: ref="${GITHUB_REF#refs/heads/}" && echo "branch=${ref////-}" >> $GITHUB_OUTPUT
        id: ref
      - name: Checkout sources
        uses: actions/checkout@v4

      - name: Validate Gradle Wrapper
        uses: gradle/actions/wrapper-validation@v3

      - name: Setup Java 21
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 21

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-read-only: false

      - name: Execute Gradle build
        run: ./gradlew build

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: sodium-artifacts-${{ steps.ref.outputs.branch }}
          path: build/mods/*.jar