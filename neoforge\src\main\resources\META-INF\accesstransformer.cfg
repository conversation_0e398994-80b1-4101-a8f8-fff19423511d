public net.minecraft.client.model.geom.ModelPart$Vertex
public net.minecraft.client.model.geom.ModelPart$Polygon
public net.minecraft.client.renderer.texture.SpriteContents$InterpolationData
public net.minecraft.client.renderer.texture.SpriteContents$AnimatedTexture
public net.minecraft.client.renderer.texture.SpriteContents$FrameInfo
public net.minecraft.client.renderer.texture.SpriteContents$Ticker
public net.minecraft.world.level.chunk.PalettedContainer$Data
public net.minecraft.world.level.chunk.PalettedContainer$Configuration
public net.minecraft.client.renderer.FogRenderer$FogData
public net.minecraft.client.renderer.FogRenderer$MobEffectFogFunction
public net.minecraft.client.renderer.texture.Stitcher$Holder
public net.minecraft.world.level.biome.Biome$ClimateSettings
public net.minecraft.client.renderer.SectionBufferBuilderPool <init>(Ljava/util/List;)V

public com.mojang.blaze3d.vertex.PoseStack$Pose <init>(Lorg/joml/Matrix4f;Lorg/joml/Matrix3f;)V
public com.mojang.blaze3d.vertex.PoseStack$Pose trustedNormals # trustedNormals

public net.minecraft.world.level.GrassColor pixels
public net.minecraft.world.level.FoliageColor pixels
public net.minecraft.client.renderer.FogRenderer getPriorityFogFunction(Lnet/minecraft/world/entity/Entity;F)Lnet/minecraft/client/renderer/FogRenderer$MobEffectFogFunction;