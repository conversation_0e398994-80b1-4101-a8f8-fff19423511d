---
name: Bug fix
about: Use this template if you're creating a pull request which fixes another bug or issue
title: ''
labels: bug
assignees: ''

---

### Preface

Make sure you have read our [Contributor Guidelines](/CONTRIBUTING.md) before submitting any pull requests to this
repository.

This section (Preface) should be removed before submitting your pull request. Doing so indicates that you have read
and agreed to the terms laid out within.

### Linked Issues
Provide links to the issue(s) which will be closed upon merging this pull request. There must be an open issue for
pull requests which fix bugs or other issues.

### Proposed Changes
Provide a detailed description of what your pull request changes.
