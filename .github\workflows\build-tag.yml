# Used when a commit is tagged and pushed to the repository
# This makes use of caching for faster builds and uploads the resulting artifacts
name: build-tag

on:
  push:
    tags:
      - '*'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Extract current branch name
        shell: bash
        # bash pattern expansion to grab branch name without slashes
        run: ref="${GITHUB_REF#refs/heads/}" && echo "branch=${ref////-}" >> $GITHUB_OUTPUT
        id: ref
      - name: Checkout sources
        uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 21

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-read-only: true

      - name: Execute Gradle build
        run: ./gradlew build -Pbuild.release=true

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: sodium-artifacts-${{ steps.ref.outputs.branch }}
          path: build/mods/*.jar